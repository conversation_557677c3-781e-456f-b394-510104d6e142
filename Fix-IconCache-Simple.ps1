# Windows Icon Cache Fix Script
# Fixes Start Menu tile icon display issues
# Version: 1.0

param(
    [switch]$Force
)

# Check Administrator privileges
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Write log
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path "$env:TEMP\IconCacheFix.log" -Value $logMessage
}

# Main function
function Main {
    Write-Log "=== Windows Icon Cache Fix Tool Started ==="
    
    if (-not (Test-Administrator)) {
        Write-Log "This script requires administrator privileges" "ERROR"
        Write-Host "Please run PowerShell as Administrator and try again." -ForegroundColor Red
        return
    }
    
    Write-Log "Administrator privileges detected, starting fix process..."
    
    # Ask for confirmation
    if (-not $Force) {
        $confirmation = Read-Host "This will restart Windows Explorer and clear icon cache. Continue? (Y/N)"
        if ($confirmation -ne 'Y' -and $confirmation -ne 'y') {
            Write-Log "User cancelled operation"
            return
        }
    }
    
    $success = $true
    
    try {
        # Stop Windows Explorer
        Write-Log "Stopping Windows Explorer..."
        Get-Process -Name "explorer" -ErrorAction SilentlyContinue | Stop-Process -Force
        Start-Sleep -Seconds 3
        Write-Log "Windows Explorer stopped"
        
        # Clear icon cache files
        Write-Log "Clearing icon cache files..."
        $iconCachePaths = @(
            "$env:LOCALAPPDATA\IconCache.db",
            "$env:LOCALAPPDATA\Microsoft\Windows\Explorer\iconcache_*.db",
            "$env:LOCALAPPDATA\Microsoft\Windows\Explorer\thumbcache_*.db"
        )
        
        $clearedFiles = 0
        foreach ($path in $iconCachePaths) {
            try {
                $files = Get-ChildItem -Path $path -Force -ErrorAction SilentlyContinue
                foreach ($file in $files) {
                    Remove-Item -Path $file.FullName -Force -ErrorAction SilentlyContinue
                    if (-not (Test-Path $file.FullName)) {
                        Write-Log "Deleted: $($file.FullName)"
                        $clearedFiles++
                    }
                }
            }
            catch {
                Write-Log "Error clearing $path : $($_.Exception.Message)" "WARNING"
            }
        }
        
        Write-Log "Icon cache cleanup completed, cleared $clearedFiles files"
        
        # Start Windows Explorer
        Write-Log "Starting Windows Explorer..."
        Start-Process "explorer.exe"
        Start-Sleep -Seconds 5
        Write-Log "Windows Explorer started"
        
        # Refresh desktop
        Write-Log "Refreshing desktop..."
        rundll32.exe user32.dll,UpdatePerUserSystemParameters
        
        Write-Log "Icon cache rebuild completed"
        
    }
    catch {
        Write-Log "Fix process failed: $($_.Exception.Message)" "ERROR"
        $success = $false
        
        # Try to restart explorer if it failed
        try {
            Start-Process "explorer.exe"
        }
        catch {
            Write-Log "Failed to restart explorer, please restart manually" "ERROR"
        }
    }
    
    if ($success) {
        Write-Log "=== Icon Cache Fix Completed ===" "SUCCESS"
        Write-Host "`nFix completed! Please check if Start Menu tiles are displaying correctly." -ForegroundColor Green
        Write-Host "If issues persist, please restart your computer." -ForegroundColor Yellow
    } else {
        Write-Log "=== Icon Cache Fix Failed ===" "ERROR"
        Write-Host "`nFix process encountered errors. Please check log file: $env:TEMP\IconCacheFix.log" -ForegroundColor Red
    }
}

# Execute main function
Main
