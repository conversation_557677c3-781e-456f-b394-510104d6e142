# 专门修复Cursor和Windsurf图标显示问题的脚本
# 针对应用程序图标资源和DPI问题

Write-Host "=== Cursor & Windsurf 图标修复工具 ===" -ForegroundColor Cyan
Write-Host "专门解决这两个应用的磁贴图标显示过大问题" -ForegroundColor White
Write-Host ""

# 检查DPI设置
function Check-DPISettings {
    Write-Host "检查DPI设置..." -ForegroundColor Yellow
    
    try {
        $dpiKey = "HKCU:\Control Panel\Desktop"
        $dpiScaling = Get-ItemProperty -Path $dpiKey -Name "LogPixels" -ErrorAction SilentlyContinue
        
        if ($dpiScaling) {
            $dpiValue = $dpiScaling.LogPixels
            $dpiPercent = ($dpiValue / 96) * 100
            Write-Host "当前DPI缩放: $dpiPercent%" -ForegroundColor Gray
            
            if ($dpiPercent -gt 100) {
                Write-Host "检测到高DPI设置，这可能是图标显示异常的原因" -ForegroundColor Yellow
                return $true
            }
        }
        return $false
    }
    catch {
        Write-Host "无法检查DPI设置" -ForegroundColor Red
        return $false
    }
}

# 查找应用程序安装路径
function Find-AppPath {
    param([string]$AppName)
    
    $commonPaths = @(
        "$env:LOCALAPPDATA\Programs\$AppName",
        "$env:PROGRAMFILES\$AppName",
        "$env:PROGRAMFILES(X86)\$AppName",
        "$env:APPDATA\$AppName"
    )
    
    foreach ($path in $commonPaths) {
        if (Test-Path $path) {
            Write-Host "找到 $AppName 安装路径: $path" -ForegroundColor Green
            return $path
        }
    }
    
    # 在注册表中查找
    try {
        $uninstallKeys = @(
            "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*",
            "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*",
            "HKCU:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*"
        )
        
        foreach ($keyPath in $uninstallKeys) {
            $apps = Get-ItemProperty $keyPath -ErrorAction SilentlyContinue
            foreach ($app in $apps) {
                if ($app.DisplayName -like "*$AppName*") {
                    if ($app.InstallLocation) {
                        Write-Host "在注册表中找到 $AppName: $($app.InstallLocation)" -ForegroundColor Green
                        return $app.InstallLocation
                    }
                }
            }
        }
    }
    catch {
        Write-Host "无法在注册表中查找 $AppName" -ForegroundColor Yellow
    }
    
    return $null
}

# 重新创建开始菜单快捷方式
function Recreate-StartMenuShortcut {
    param([string]$AppName, [string]$AppPath)
    
    Write-Host "重新创建 $AppName 的开始菜单快捷方式..." -ForegroundColor Yellow
    
    try {
        # 查找可执行文件
        $exeFiles = Get-ChildItem -Path $AppPath -Filter "*.exe" -Recurse | Where-Object { $_.Name -like "*$AppName*" }
        
        if ($exeFiles.Count -eq 0) {
            $exeFiles = Get-ChildItem -Path $AppPath -Filter "*.exe" -Recurse | Select-Object -First 1
        }
        
        if ($exeFiles.Count -gt 0) {
            $exePath = $exeFiles[0].FullName
            Write-Host "找到可执行文件: $exePath" -ForegroundColor Gray
            
            # 创建快捷方式
            $startMenuPath = "$env:APPDATA\Microsoft\Windows\Start Menu\Programs"
            $shortcutPath = "$startMenuPath\$AppName.lnk"
            
            # 删除旧的快捷方式
            if (Test-Path $shortcutPath) {
                Remove-Item $shortcutPath -Force
            }
            
            # 创建新的快捷方式
            $WshShell = New-Object -comObject WScript.Shell
            $Shortcut = $WshShell.CreateShortcut($shortcutPath)
            $Shortcut.TargetPath = $exePath
            $Shortcut.WorkingDirectory = Split-Path $exePath
            $Shortcut.Save()
            
            Write-Host "✓ $AppName 快捷方式已重新创建" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "未找到 $AppName 的可执行文件" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "创建 $AppName 快捷方式失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 修复DPI兼容性
function Fix-DPICompatibility {
    param([string]$AppName, [string]$ExePath)
    
    Write-Host "修复 $AppName 的DPI兼容性..." -ForegroundColor Yellow
    
    try {
        $regPath = "HKCU:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\AppCompatFlags\Layers"
        
        # 确保注册表路径存在
        if (-not (Test-Path $regPath)) {
            New-Item -Path $regPath -Force | Out-Null
        }
        
        # 设置DPI兼容性
        Set-ItemProperty -Path $regPath -Name $ExePath -Value "HIGHDPIAWARE" -Force
        
        Write-Host "✓ $AppName DPI兼容性已设置" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "设置 $AppName DPI兼容性失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 清理特定应用的缓存
function Clear-AppSpecificCache {
    param([string]$AppName)
    
    Write-Host "清理 $AppName 的特定缓存..." -ForegroundColor Yellow
    
    $cachePaths = @(
        "$env:LOCALAPPDATA\$AppName",
        "$env:APPDATA\$AppName",
        "$env:TEMP\$AppName*"
    )
    
    foreach ($path in $cachePaths) {
        try {
            if (Test-Path $path) {
                $cacheFiles = Get-ChildItem -Path $path -Filter "*cache*" -Recurse -ErrorAction SilentlyContinue
                foreach ($file in $cacheFiles) {
                    Remove-Item -Path $file.FullName -Recurse -Force -ErrorAction SilentlyContinue
                }
                Write-Host "  清理了 $path 中的缓存文件" -ForegroundColor Gray
            }
        }
        catch {
            # 忽略清理错误
        }
    }
    
    Write-Host "✓ $AppName 缓存清理完成" -ForegroundColor Green
}

# 主修复流程
function Main {
    $apps = @("Cursor", "Windsurf")
    $isDPIIssue = Check-DPISettings
    
    foreach ($app in $apps) {
        Write-Host ""
        Write-Host "处理 $app..." -ForegroundColor Cyan
        
        # 查找应用路径
        $appPath = Find-AppPath -AppName $app
        
        if ($appPath) {
            # 清理应用特定缓存
            Clear-AppSpecificCache -AppName $app
            
            # 重新创建快捷方式
            $shortcutCreated = Recreate-StartMenuShortcut -AppName $app -AppPath $appPath
            
            # 如果是DPI问题，设置兼容性
            if ($isDPIIssue -and $shortcutCreated) {
                $exeFiles = Get-ChildItem -Path $appPath -Filter "*.exe" -Recurse | Where-Object { $_.Name -like "*$app*" }
                if ($exeFiles.Count -gt 0) {
                    Fix-DPICompatibility -AppName $app -ExePath $exeFiles[0].FullName
                }
            }
        }
        else {
            Write-Host "未找到 $app 的安装路径，可能需要重新安装" -ForegroundColor Red
        }
    }
    
    # 最终刷新
    Write-Host ""
    Write-Host "刷新系统..." -ForegroundColor Yellow
    Stop-Process -Name "explorer" -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 3
    Start-Process "explorer.exe"
    Start-Sleep -Seconds 3
    rundll32.exe user32.dll,UpdatePerUserSystemParameters
    
    Write-Host ""
    Write-Host "=== 修复完成 ===" -ForegroundColor Green
    Write-Host "请检查开始菜单中的Cursor和Windsurf磁贴" -ForegroundColor White
    Write-Host ""
    Write-Host "如果问题仍然存在，建议:" -ForegroundColor Yellow
    Write-Host "1. 右键点击磁贴 → 调整大小 → 选择合适的大小" -ForegroundColor White
    Write-Host "2. 重新安装这两个应用程序" -ForegroundColor White
    Write-Host "3. 检查显示器DPI设置" -ForegroundColor White
}

# 执行主函数
Main

Read-Host "按回车键退出"
