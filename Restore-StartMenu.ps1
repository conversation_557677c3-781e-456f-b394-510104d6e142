# Windows 开始菜单恢复脚本
# 从备份恢复开始菜单布局和磁贴配置
# 作者: AI助手
# 版本: 1.0

param(
    [string]$BackupPath = "$env:USERPROFILE\Documents\StartMenuBackups",
    [string]$RestoreDate = "",
    [switch]$Force,
    [switch]$Verbose
)

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 写入日志
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    if ($Verbose) {
        Add-Content -Path "$env:TEMP\StartMenuRestore.log" -Value $logMessage
    }
}

# 获取可用的备份
function Get-AvailableBackups {
    param([string]$BackupDir)
    
    if (-not (Test-Path $BackupDir)) {
        Write-Log "备份目录不存在: $BackupDir" "ERROR"
        return @()
    }
    
    $backups = @()
    
    # 查找布局文件备份
    $layoutFiles = Get-ChildItem -Path $BackupDir -Filter "StartMenuLayout_*.xml" | Sort-Object CreationTime -Descending
    
    foreach ($file in $layoutFiles) {
        if ($file.Name -match "StartMenuLayout_(\d{8}_\d{6})\.xml") {
            $dateStr = $matches[1]
            $backupDate = [DateTime]::ParseExact($dateStr, "yyyyMMdd_HHmmss", $null)
            
            $backup = @{
                Date = $backupDate
                DateString = $dateStr
                LayoutFile = $file.FullName
                ShortcutsDir = Join-Path $BackupDir "StartMenuShortcuts_$dateStr"
                RegistryFile = Join-Path $BackupDir "StartMenuRegistry_$dateStr.reg"
                InfoFile = Join-Path $BackupDir "BackupInfo_$dateStr.txt"
            }
            
            $backups += $backup
        }
    }
    
    return $backups
}

# 显示可用备份列表
function Show-BackupList {
    param([array]$Backups)
    
    if ($Backups.Count -eq 0) {
        Write-Host "没有找到可用的备份。" -ForegroundColor Yellow
        return $null
    }
    
    Write-Host "`n可用的备份:" -ForegroundColor Cyan
    Write-Host "序号`t日期时间`t`t`t备份内容" -ForegroundColor Gray
    Write-Host "----`t--------`t`t`t--------" -ForegroundColor Gray
    
    for ($i = 0; $i -lt $Backups.Count; $i++) {
        $backup = $Backups[$i]
        $content = @()
        
        if (Test-Path $backup.LayoutFile) { $content += "布局" }
        if (Test-Path $backup.ShortcutsDir) { $content += "快捷方式" }
        if (Test-Path $backup.RegistryFile) { $content += "注册表" }
        
        $contentStr = $content -join ", "
        Write-Host "$($i+1)`t$($backup.Date.ToString('yyyy-MM-dd HH:mm:ss'))`t$contentStr"
    }
    
    return $Backups
}

# 选择备份
function Select-Backup {
    param([array]$Backups, [string]$DateFilter)
    
    if ($DateFilter) {
        $selectedBackup = $Backups | Where-Object { $_.DateString -like "*$DateFilter*" } | Select-Object -First 1
        if ($selectedBackup) {
            Write-Log "根据日期筛选选择备份: $($selectedBackup.Date)"
            return $selectedBackup
        } else {
            Write-Log "未找到匹配日期 '$DateFilter' 的备份" "WARNING"
        }
    }
    
    $backups = Show-BackupList -Backups $Backups
    if (-not $backups) { return $null }
    
    do {
        $selection = Read-Host "`n请选择要恢复的备份 (1-$($backups.Count)，或按 Q 退出)"
        if ($selection -eq 'Q' -or $selection -eq 'q') {
            return $null
        }
        
        $index = [int]$selection - 1
    } while ($index -lt 0 -or $index -ge $backups.Count)
    
    return $backups[$index]
}

# 恢复开始菜单布局
function Restore-StartMenuLayout {
    param([string]$LayoutFile)
    
    if (-not (Test-Path $LayoutFile)) {
        Write-Log "布局文件不存在: $LayoutFile" "WARNING"
        return $false
    }
    
    Write-Log "正在恢复开始菜单布局..."
    
    try {
        Import-StartLayout -LayoutPath $LayoutFile -MountPath C:\
        Write-Log "开始菜单布局恢复成功"
        return $true
    }
    catch {
        Write-Log "恢复开始菜单布局失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 恢复快捷方式
function Restore-StartMenuShortcuts {
    param([string]$ShortcutsDir)
    
    if (-not (Test-Path $ShortcutsDir)) {
        Write-Log "快捷方式备份目录不存在: $ShortcutsDir" "WARNING"
        return $false
    }
    
    Write-Log "正在恢复开始菜单快捷方式..."
    
    try {
        # 恢复用户开始菜单
        $userBackupPath = Join-Path $ShortcutsDir "User"
        if (Test-Path $userBackupPath) {
            $userStartMenu = "$env:APPDATA\Microsoft\Windows\Start Menu"
            if (Test-Path $userStartMenu) {
                Remove-Item -Path $userStartMenu -Recurse -Force
            }
            Copy-Item -Path $userBackupPath -Destination $userStartMenu -Recurse -Force
            Write-Log "用户开始菜单快捷方式已恢复"
        }
        
        # 恢复公共开始菜单 (需要管理员权限)
        $commonBackupPath = Join-Path $ShortcutsDir "Common"
        if ((Test-Path $commonBackupPath) -and (Test-Administrator)) {
            $commonStartMenu = "$env:ProgramData\Microsoft\Windows\Start Menu"
            if (Test-Path $commonStartMenu) {
                Remove-Item -Path $commonStartMenu -Recurse -Force
            }
            Copy-Item -Path $commonBackupPath -Destination $commonStartMenu -Recurse -Force
            Write-Log "公共开始菜单快捷方式已恢复"
        }
        
        return $true
    }
    catch {
        Write-Log "恢复开始菜单快捷方式失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 恢复注册表
function Restore-StartMenuRegistry {
    param([string]$RegistryFile)
    
    if (-not (Test-Path $RegistryFile)) {
        Write-Log "注册表备份文件不存在: $RegistryFile" "WARNING"
        return $false
    }
    
    Write-Log "正在恢复开始菜单注册表..."
    
    try {
        $result = Start-Process -FilePath "reg" -ArgumentList "import", "`"$RegistryFile`"" -Wait -PassThru -WindowStyle Hidden
        if ($result.ExitCode -eq 0) {
            Write-Log "开始菜单注册表恢复成功"
            return $true
        } else {
            Write-Log "恢复开始菜单注册表失败，退出代码: $($result.ExitCode)" "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "恢复开始菜单注册表失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 重启Windows资源管理器
function Restart-Explorer {
    Write-Log "正在重启Windows资源管理器..."
    
    try {
        Get-Process -Name "explorer" -ErrorAction SilentlyContinue | Stop-Process -Force
        Start-Sleep -Seconds 2
        Start-Process "explorer.exe"
        Start-Sleep -Seconds 3
        Write-Log "Windows资源管理器已重启"
        return $true
    }
    catch {
        Write-Log "重启Windows资源管理器失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 主函数
function Main {
    Write-Log "=== Windows 开始菜单恢复工具启动 ==="
    
    # 获取可用备份
    $backups = Get-AvailableBackups -BackupDir $BackupPath
    if ($backups.Count -eq 0) {
        Write-Host "在 $BackupPath 中没有找到可用的备份。" -ForegroundColor Red
        Write-Host "请先运行 Backup-StartMenu.ps1 创建备份。" -ForegroundColor Yellow
        return
    }
    
    # 选择要恢复的备份
    $selectedBackup = Select-Backup -Backups $backups -DateFilter $RestoreDate
    if (-not $selectedBackup) {
        Write-Log "用户取消恢复操作"
        return
    }
    
    # 显示恢复信息
    Write-Host "`n将要恢复的备份:" -ForegroundColor Cyan
    Write-Host "日期: $($selectedBackup.Date)" -ForegroundColor White
    if (Test-Path $selectedBackup.InfoFile) {
        $info = Get-Content $selectedBackup.InfoFile -Encoding UTF8
        $info[0..6] | ForEach-Object { Write-Host $_ -ForegroundColor Gray }
    }
    
    # 确认恢复
    if (-not $Force) {
        $confirmation = Read-Host "`n此操作将覆盖当前的开始菜单配置。是否继续？(Y/N)"
        if ($confirmation -ne 'Y' -and $confirmation -ne 'y') {
            Write-Log "用户取消恢复操作"
            return
        }
    }
    
    # 执行恢复
    Write-Log "开始恢复操作..."
    $results = @{}
    
    $results["注册表"] = Restore-StartMenuRegistry -RegistryFile $selectedBackup.RegistryFile
    $results["快捷方式"] = Restore-StartMenuShortcuts -ShortcutsDir $selectedBackup.ShortcutsDir
    $results["布局"] = Restore-StartMenuLayout -LayoutFile $selectedBackup.LayoutFile
    
    # 重启资源管理器
    if ($results.Values -contains $true) {
        Restart-Explorer
    }
    
    # 显示结果
    $successCount = ($results.Values | Where-Object { $_ }).Count
    $totalCount = $results.Count
    
    Write-Log "=== 恢复完成 ==="
    Write-Host "`n恢复结果: $successCount/$totalCount 项成功" -ForegroundColor $(if($successCount -eq $totalCount){"Green"}else{"Yellow"})
    
    foreach ($key in $results.Keys) {
        $status = if ($results[$key]) { "成功" } else { "失败" }
        $color = if ($results[$key]) { "Green" } else { "Red" }
        Write-Host "  $key : $status" -ForegroundColor $color
    }
    
    if ($successCount -gt 0) {
        Write-Host "`n建议重启计算机以确保所有更改生效。" -ForegroundColor Yellow
    }
    
    if ($Verbose) {
        Write-Host "详细日志: $env:TEMP\StartMenuRestore.log" -ForegroundColor Gray
    }
}

# 执行主函数
Main
