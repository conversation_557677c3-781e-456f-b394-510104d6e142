# Windows 开始菜单综合修复脚本
# 一键修复开始菜单磁贴显示异常和消失问题
# 作者: AI助手
# 版本: 1.0

param(
    [switch]$Force,
    [switch]$Verbose,
    [switch]$SkipBackup
)

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 写入日志
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path "$env:TEMP\StartMenuFix.log" -Value $logMessage
}

# 显示进度
function Write-Progress-Custom {
    param([string]$Activity, [string]$Status, [int]$PercentComplete)
    Write-Progress -Activity $Activity -Status $Status -PercentComplete $PercentComplete
    Write-Log "$Activity - $Status ($PercentComplete%)"
}

# 创建备份
function New-EmergencyBackup {
    Write-Progress-Custom -Activity "开始菜单修复" -Status "创建紧急备份..." -PercentComplete 10
    
    try {
        $backupScript = Join-Path $PSScriptRoot "Backup-StartMenu.ps1"
        if (Test-Path $backupScript) {
            & $backupScript -Verbose:$Verbose
            Write-Log "紧急备份创建完成"
            return $true
        } else {
            Write-Log "备份脚本不存在，跳过备份" "WARNING"
            return $false
        }
    }
    catch {
        Write-Log "创建紧急备份失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 修复图标缓存
function Repair-IconCache {
    Write-Progress-Custom -Activity "开始菜单修复" -Status "修复图标缓存..." -PercentComplete 30
    
    try {
        $iconCacheScript = Join-Path $PSScriptRoot "Fix-IconCache.ps1"
        if (Test-Path $iconCacheScript) {
            & $iconCacheScript -Force -Verbose:$Verbose
            Write-Log "图标缓存修复完成"
            return $true
        } else {
            Write-Log "图标缓存修复脚本不存在" "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "修复图标缓存失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 重置开始菜单数据库
function Reset-StartMenuDatabase {
    Write-Progress-Custom -Activity "开始菜单修复" -Status "重置开始菜单数据库..." -PercentComplete 50
    
    try {
        # 停止相关服务
        $services = @("ShellHWDetection", "Themes")
        foreach ($service in $services) {
            try {
                Stop-Service -Name $service -Force -ErrorAction SilentlyContinue
                Write-Log "已停止服务: $service"
            }
            catch {
                Write-Log "停止服务 $service 失败: $($_.Exception.Message)" "WARNING"
            }
        }
        
        # 清理开始菜单缓存
        $cachePaths = @(
            "$env:LOCALAPPDATA\Microsoft\Windows\Caches",
            "$env:LOCALAPPDATA\Microsoft\Windows\Explorer\*.db",
            "$env:LOCALAPPDATA\Packages\Microsoft.Windows.StartMenuExperienceHost_*\TempState"
        )
        
        foreach ($path in $cachePaths) {
            try {
                $items = Get-ChildItem -Path $path -Force -ErrorAction SilentlyContinue
                foreach ($item in $items) {
                    Remove-Item -Path $item.FullName -Recurse -Force -ErrorAction SilentlyContinue
                    Write-Log "已清理: $($item.FullName)"
                }
            }
            catch {
                Write-Log "清理 $path 失败: $($_.Exception.Message)" "WARNING"
            }
        }
        
        # 重启服务
        foreach ($service in $services) {
            try {
                Start-Service -Name $service -ErrorAction SilentlyContinue
                Write-Log "已启动服务: $service"
            }
            catch {
                Write-Log "启动服务 $service 失败: $($_.Exception.Message)" "WARNING"
            }
        }
        
        Write-Log "开始菜单数据库重置完成"
        return $true
    }
    catch {
        Write-Log "重置开始菜单数据库失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 修复开始菜单注册表
function Repair-StartMenuRegistry {
    Write-Progress-Custom -Activity "开始菜单修复" -Status "修复开始菜单注册表..." -PercentComplete 70
    
    try {
        # 重新注册开始菜单相关组件
        $commands = @(
            'Get-AppXPackage -AllUsers | Foreach {Add-AppxPackage -DisableDevelopmentMode -Register "$($_.InstallLocation)\AppXManifest.xml"}',
            'Get-AppXPackage -Name Microsoft.Windows.StartMenuExperienceHost | Foreach {Add-AppxPackage -DisableDevelopmentMode -Register "$($_.InstallLocation)\AppXManifest.xml"}',
            'Get-AppXPackage -Name Microsoft.Windows.Cortana | Foreach {Add-AppxPackage -DisableDevelopmentMode -Register "$($_.InstallLocation)\AppXManifest.xml"}'
        )
        
        foreach ($command in $commands) {
            try {
                Invoke-Expression $command
                Write-Log "执行命令成功: $($command.Substring(0, [Math]::Min(50, $command.Length)))..."
            }
            catch {
                Write-Log "执行命令失败: $($_.Exception.Message)" "WARNING"
            }
        }
        
        # 修复关键注册表项
        $regFixes = @{
            "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" = @{
                "EnableXamlStartMenu" = 1
                "Start_ShowClassicMode" = 0
            }
        }
        
        foreach ($regPath in $regFixes.Keys) {
            try {
                if (-not (Test-Path $regPath)) {
                    New-Item -Path $regPath -Force | Out-Null
                }
                
                foreach ($valueName in $regFixes[$regPath].Keys) {
                    $value = $regFixes[$regPath][$valueName]
                    Set-ItemProperty -Path $regPath -Name $valueName -Value $value -Force
                    Write-Log "设置注册表项: $regPath\$valueName = $value"
                }
            }
            catch {
                Write-Log "修复注册表项 $regPath 失败: $($_.Exception.Message)" "WARNING"
            }
        }
        
        Write-Log "开始菜单注册表修复完成"
        return $true
    }
    catch {
        Write-Log "修复开始菜单注册表失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 重建开始菜单
function Rebuild-StartMenu {
    Write-Progress-Custom -Activity "开始菜单修复" -Status "重建开始菜单..." -PercentComplete 90
    
    try {
        # 重启Windows资源管理器
        Get-Process -Name "explorer" -ErrorAction SilentlyContinue | Stop-Process -Force
        Start-Sleep -Seconds 3
        Start-Process "explorer.exe"
        Start-Sleep -Seconds 5
        
        # 刷新系统
        Add-Type -TypeDefinition @"
            using System;
            using System.Runtime.InteropServices;
            public class Win32 {
                [DllImport("user32.dll", SetLastError = true)]
                public static extern bool SystemParametersInfo(uint uiAction, uint uiParam, IntPtr pvParam, uint fWinIni);
                [DllImport("shell32.dll")]
                public static extern void SHChangeNotify(uint wEventId, uint uFlags, IntPtr dwItem1, IntPtr dwItem2);
            }
"@
        
        # 刷新桌面和开始菜单
        [Win32]::SystemParametersInfo(0x0014, 0, [IntPtr]::Zero, 0x0001)  # SPI_SETWORKAREA
        [Win32]::SHChangeNotify(0x8000000, 0x0000, [IntPtr]::Zero, [IntPtr]::Zero)  # SHCNE_ASSOCCHANGED
        
        Write-Log "开始菜单重建完成"
        return $true
    }
    catch {
        Write-Log "重建开始菜单失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 验证修复结果
function Test-StartMenuHealth {
    Write-Progress-Custom -Activity "开始菜单修复" -Status "验证修复结果..." -PercentComplete 95
    
    try {
        $issues = @()
        
        # 检查开始菜单进程
        if (-not (Get-Process -Name "StartMenuExperienceHost" -ErrorAction SilentlyContinue)) {
            $issues += "开始菜单进程未运行"
        }
        
        # 检查关键文件
        $criticalPaths = @(
            "$env:LOCALAPPDATA\Microsoft\Windows\Explorer",
            "$env:APPDATA\Microsoft\Windows\Start Menu"
        )
        
        foreach ($path in $criticalPaths) {
            if (-not (Test-Path $path)) {
                $issues += "关键路径不存在: $path"
            }
        }
        
        if ($issues.Count -eq 0) {
            Write-Log "开始菜单健康检查通过"
            return $true
        } else {
            foreach ($issue in $issues) {
                Write-Log "发现问题: $issue" "WARNING"
            }
            return $false
        }
    }
    catch {
        Write-Log "验证修复结果失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 主函数
function Main {
    Write-Log "=== Windows 开始菜单综合修复工具启动 ==="
    
    if (-not (Test-Administrator)) {
        Write-Log "此脚本需要管理员权限运行" "ERROR"
        Write-Host "请以管理员身份运行PowerShell，然后重新执行此脚本。" -ForegroundColor Red
        return
    }
    
    Write-Host "Windows 开始菜单综合修复工具" -ForegroundColor Cyan
    Write-Host "此工具将修复开始菜单磁贴显示异常和消失问题" -ForegroundColor Gray
    
    # 询问用户确认
    if (-not $Force) {
        Write-Host "`n修复过程包括:" -ForegroundColor Yellow
        Write-Host "1. 创建紧急备份 (如果未跳过)" -ForegroundColor White
        Write-Host "2. 修复图标缓存" -ForegroundColor White
        Write-Host "3. 重置开始菜单数据库" -ForegroundColor White
        Write-Host "4. 修复开始菜单注册表" -ForegroundColor White
        Write-Host "5. 重建开始菜单" -ForegroundColor White
        
        $confirmation = Read-Host "`n是否继续执行修复？(Y/N)"
        if ($confirmation -ne 'Y' -and $confirmation -ne 'y') {
            Write-Log "用户取消修复操作"
            return
        }
    }
    
    $results = @{}
    $startTime = Get-Date
    
    # 执行修复步骤
    if (-not $SkipBackup) {
        $results["备份"] = New-EmergencyBackup
    } else {
        Write-Log "跳过备份步骤"
        $results["备份"] = $true
    }
    
    $results["图标缓存"] = Repair-IconCache
    $results["数据库重置"] = Reset-StartMenuDatabase
    $results["注册表修复"] = Repair-StartMenuRegistry
    $results["菜单重建"] = Rebuild-StartMenu
    $results["健康检查"] = Test-StartMenuHealth
    
    Write-Progress -Activity "开始菜单修复" -Completed
    
    # 显示结果
    $endTime = Get-Date
    $duration = $endTime - $startTime
    $successCount = ($results.Values | Where-Object { $_ }).Count
    $totalCount = $results.Count
    
    Write-Log "=== 修复完成 ==="
    Write-Host "`n修复结果: $successCount/$totalCount 项成功" -ForegroundColor $(if($successCount -eq $totalCount){"Green"}else{"Yellow"})
    Write-Host "耗时: $($duration.TotalSeconds.ToString('F1')) 秒" -ForegroundColor Gray
    
    foreach ($key in $results.Keys) {
        $status = if ($results[$key]) { "成功" } else { "失败" }
        $color = if ($results[$key]) { "Green" } else { "Red" }
        Write-Host "  $key : $status" -ForegroundColor $color
    }
    
    if ($successCount -eq $totalCount) {
        Write-Host "`n✓ 开始菜单修复完成！请检查磁贴是否恢复正常。" -ForegroundColor Green
    } else {
        Write-Host "`n⚠ 部分修复步骤失败，建议重启计算机后重新运行此脚本。" -ForegroundColor Yellow
    }
    
    Write-Host "`n建议:" -ForegroundColor Cyan
    Write-Host "1. 重启计算机以确保所有更改生效" -ForegroundColor White
    Write-Host "2. 定期运行 Backup-StartMenu.ps1 创建备份" -ForegroundColor White
    Write-Host "3. 如果问题持续，可尝试从备份恢复" -ForegroundColor White
    
    Write-Host "`n详细日志: $env:TEMP\StartMenuFix.log" -ForegroundColor Gray
}

# 执行主函数
Main
