# Windows 图标缓存修复脚本
# 解决开始菜单磁贴图标显示异常问题
# 作者: AI助手
# 版本: 1.0

param(
    [switch]$Force,
    [switch]$Verbose
)

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 写入日志
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path "$env:TEMP\IconCacheFix.log" -Value $logMessage
}

# 停止Windows资源管理器
function Stop-Explorer {
    Write-Log "正在停止Windows资源管理器..."
    try {
        Get-Process -Name "explorer" -ErrorAction SilentlyContinue | Stop-Process -Force
        Start-Sleep -Seconds 2
        Write-Log "Windows资源管理器已停止"
        return $true
    }
    catch {
        Write-Log "停止Windows资源管理器失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 启动Windows资源管理器
function Start-Explorer {
    Write-Log "正在启动Windows资源管理器..."
    try {
        Start-Process "explorer.exe"
        Start-Sleep -Seconds 3
        Write-Log "Windows资源管理器已启动"
        return $true
    }
    catch {
        Write-Log "启动Windows资源管理器失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 清理图标缓存文件
function Clear-IconCache {
    Write-Log "开始清理图标缓存..."

    $iconCachePaths = @(
        "$env:LOCALAPPDATA\IconCache.db",
        "$env:LOCALAPPDATA\Microsoft\Windows\Explorer\iconcache_*.db",
        "$env:LOCALAPPDATA\Microsoft\Windows\Explorer\thumbcache_*.db"
    )

    $clearedFiles = 0

    foreach ($path in $iconCachePaths) {
        try {
            $files = Get-ChildItem -Path $path -Force -ErrorAction SilentlyContinue
            foreach ($file in $files) {
                Remove-Item -Path $file.FullName -Force -ErrorAction SilentlyContinue
                if (-not (Test-Path $file.FullName)) {
                    Write-Log "已删除: $($file.FullName)"
                    $clearedFiles++
                }
            }
        }
        catch {
            Write-Log "清理 $path 时出错: $($_.Exception.Message)" "WARNING"
        }
    }

    Write-Log "图标缓存清理完成，共清理 $clearedFiles 个文件"
    return $clearedFiles -gt 0
}

# 重建图标缓存
function Rebuild-IconCache {
    Write-Log "开始重建图标缓存..."

    try {
        # 刷新图标缓存
        $shell = New-Object -ComObject Shell.Application
        $shell.RefreshMenu()

        # 刷新桌面 - 使用简化方法
        rundll32.exe user32.dll,UpdatePerUserSystemParameters

        Write-Log "图标缓存重建完成"
        return $true
    }
    catch {
        Write-Log "重建图标缓存失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 主函数
function Main {
    Write-Log "=== Windows 图标缓存修复工具启动 ==="
    
    if (-not (Test-Administrator)) {
        Write-Log "此脚本需要管理员权限运行" "ERROR"
        Write-Host "请以管理员身份运行PowerShell，然后重新执行此脚本。" -ForegroundColor Red
        return
    }
    
    Write-Log "检测到管理员权限，开始修复过程..."
    
    # 询问用户确认
    if (-not $Force) {
        $confirmation = Read-Host "此操作将重启Windows资源管理器并清理图标缓存。是否继续？(Y/N)"
        if ($confirmation -ne 'Y' -and $confirmation -ne 'y') {
            Write-Log "用户取消操作"
            return
        }
    }
    
    $success = $true
    
    # 停止资源管理器
    if (-not (Stop-Explorer)) {
        $success = $false
    }
    
    # 清理图标缓存
    if ($success) {
        if (-not (Clear-IconCache)) {
            Write-Log "图标缓存清理失败，但继续执行..." "WARNING"
        }
    }
    
    # 启动资源管理器
    if (-not (Start-Explorer)) {
        Write-Log "资源管理器启动失败，请手动启动" "ERROR"
        $success = $false
    }
    
    # 重建图标缓存
    if ($success) {
        Start-Sleep -Seconds 5  # 等待资源管理器完全启动
        Rebuild-IconCache
    }
    
    if ($success) {
        Write-Log "=== 图标缓存修复完成 ===" "SUCCESS"
        Write-Host "`n修复完成！请检查开始菜单磁贴是否恢复正常。" -ForegroundColor Green
        Write-Host "如果问题仍然存在，请重启计算机后再次检查。" -ForegroundColor Yellow
    } else {
        Write-Log "=== 图标缓存修复失败 ===" "ERROR"
        Write-Host "`n修复过程中出现错误，请查看日志文件: $env:TEMP\IconCacheFix.log" -ForegroundColor Red
    }
}

# 执行主函数
Main
