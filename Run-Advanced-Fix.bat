@echo off
echo ========================================
echo Advanced Icon Cache Fix Tool
echo ========================================
echo.
echo This will fix Cursor and Windsurf icon display issues
echo using advanced methods with administrator privileges.
echo.
echo IMPORTANT: 
echo - This will temporarily close all windows
echo - Save your work before continuing
echo - The process may take 1-2 minutes
echo.
echo Press any key to start the fix, or close this window to cancel.
pause >nul

echo.
echo Starting advanced fix with administrator privileges...
powershell.exe -Command "Start-Process PowerShell -ArgumentList '-ExecutionPolicy Bypass -File \"%~dp0Fix-Icons-Advanced.ps1\"' -Verb RunAs -Wait"

echo.
echo Advanced fix process completed.
echo Please check your Start Menu tiles.
echo.
pause
