@echo off
chcp 65001 >nul
echo ========================================
echo Cursor和Windsurf磁贴图标修复工具
echo ========================================
echo.
echo 这个工具将尝试修复磁贴图标显示过大的问题
echo.
pause

echo.
echo 步骤1: 重置开始菜单布局...
powershell -Command "Get-Process -Name 'StartMenuExperienceHost' -ErrorAction SilentlyContinue | Stop-Process -Force"

echo 步骤2: 清理开始菜单缓存...
rd /s /q "%LOCALAPPDATA%\Packages\Microsoft.Windows.StartMenuExperienceHost_cw5n1h2txyewy\TempState" 2>nul
rd /s /q "%LOCALAPPDATA%\TileDataLayer" 2>nul

echo 步骤3: 重启Windows资源管理器...
taskkill /f /im explorer.exe >nul 2>&1
timeout /t 3 /nobreak >nul
start explorer.exe

echo 步骤4: 等待系统稳定...
timeout /t 5 /nobreak >nul

echo 步骤5: 刷新开始菜单...
powershell -Command "$shell = New-Object -ComObject Shell.Application; $shell.RefreshMenu()"

echo.
echo ========================================
echo 修复完成！
echo ========================================
echo.
echo 现在请尝试以下操作：
echo 1. 打开开始菜单
echo 2. 右键点击Cursor磁贴 ^> 调整大小 ^> 选择"小"
echo 3. 右键点击Windsurf磁贴 ^> 调整大小 ^> 选择"小"
echo 4. 如果磁贴仍然异常，请尝试：
echo    - 右键磁贴 ^> 从"开始"屏幕取消固定
echo    - 重新搜索应用并固定到开始屏幕
echo.
echo 如果问题仍然存在，可能需要重新安装这两个应用程序
echo.
pause
