# Windows 10 Start Menu Reset Tool
# Specifically for fixing tile icon display issues

Write-Host "Windows 10 Start Menu Reset Tool" -ForegroundColor Cyan
Write-Host "Fixing Cursor and Windsurf icon display issues..." -ForegroundColor White
Write-Host ""

try {
    # Method 1: PowerShell App Reset
    Write-Host "Method 1: Resetting Start Menu apps..." -ForegroundColor Yellow
    
    # Reset Start Menu Experience Host
    Get-AppxPackage Microsoft.Windows.StartMenuExperienceHost | Reset-AppxPackage
    
    # Reset Cortana (affects Start Menu in Windows 10)
    Get-AppxPackage Microsoft.Windows.Cortana | Reset-AppxPackage
    
    Write-Host "✓ Start Menu apps reset" -ForegroundColor Green
    
    # Method 2: Clear Start Menu cache
    Write-Host "Method 2: Clearing Start Menu cache..." -ForegroundColor Yellow
    
    $startMenuCache = "$env:LOCALAPPDATA\Packages\Microsoft.Windows.StartMenuExperienceHost_cw5n1h2txyewy\TempState"
    if (Test-Path $startMenuCache) {
        Remove-Item -Path "$startMenuCache\*" -Recurse -Force -ErrorAction SilentlyContinue
        Write-Host "✓ Start Menu cache cleared" -ForegroundColor Green
    }
    
    # Method 3: Reset tile database
    Write-Host "Method 3: Resetting tile database..." -ForegroundColor Yellow
    
    $tileDataLayer = "$env:LOCALAPPDATA\TileDataLayer"
    if (Test-Path $tileDataLayer) {
        Remove-Item -Path "$tileDataLayer\Database" -Recurse -Force -ErrorAction SilentlyContinue
        Write-Host "✓ Tile database reset" -ForegroundColor Green
    }
    
    # Method 4: Restart Windows Explorer
    Write-Host "Method 4: Restarting Windows Explorer..." -ForegroundColor Yellow
    
    Stop-Process -Name "explorer" -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 3
    Start-Process "explorer.exe"
    Start-Sleep -Seconds 5
    
    Write-Host "✓ Windows Explorer restarted" -ForegroundColor Green
    
    # Method 5: Force refresh
    Write-Host "Method 5: Force refreshing Start Menu..." -ForegroundColor Yellow
    
    # Use multiple refresh methods
    rundll32.exe user32.dll,UpdatePerUserSystemParameters
    rundll32.exe shell32.dll,SHChangeNotify,0x8000000,0x1000,0,0
    
    # Trigger Start Menu refresh
    $wshell = New-Object -ComObject wscript.shell
    $wshell.SendKeys('^{ESC}')  # Ctrl+Esc to open Start Menu
    Start-Sleep -Seconds 1
    $wshell.SendKeys('{ESC}')   # Esc to close Start Menu
    
    Write-Host "✓ Start Menu refreshed" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "=== Start Menu Reset Completed! ===" -ForegroundColor Green
    Write-Host "Please check your Start Menu tiles now." -ForegroundColor White
    Write-Host ""
    Write-Host "If Cursor and Windsurf icons are still not displaying correctly:" -ForegroundColor Yellow
    Write-Host "1. Try logging out and back in" -ForegroundColor White
    Write-Host "2. Restart your computer" -ForegroundColor White
    Write-Host "3. Check if the applications need to be reinstalled" -ForegroundColor White

}
catch {
    Write-Host ""
    Write-Host "=== Reset Failed ===" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please try:" -ForegroundColor Yellow
    Write-Host "1. Running this script as Administrator" -ForegroundColor White
    Write-Host "2. Restarting your computer" -ForegroundColor White
    Write-Host "3. Manually resetting Start Menu from Settings" -ForegroundColor White
}

Write-Host ""
Read-Host "Press Enter to exit"
