# Windows 开始菜单修复工具集

这是一套完整的 Windows 开始菜单修复和维护工具，专门解决开始菜单磁贴图标显示异常、磁贴消失等问题。

## 🚀 功能特性

- **图标缓存修复** - 解决磁贴图标显示过大或异常问题
- **开始菜单备份** - 定期备份开始菜单配置，防止数据丢失
- **开始菜单恢复** - 从备份快速恢复开始菜单配置
- **综合修复** - 一键修复所有常见开始菜单问题
- **自动维护** - 设置定期维护任务，预防问题发生

## 📁 文件说明

| 文件名 | 功能描述 |
|--------|----------|
| `Fix-IconCache.ps1` | 修复 Windows 图标缓存，解决磁贴图标显示异常 |
| `Backup-StartMenu.ps1` | 备份开始菜单布局、快捷方式和注册表配置 |
| `Restore-StartMenu.ps1` | 从备份恢复开始菜单配置 |
| `Fix-StartMenu.ps1` | 综合修复脚本，一键解决所有常见问题 |
| `Setup-StartMenuMaintenance.ps1` | 设置自动维护任务，预防问题发生 |

## 🛠️ 使用方法

### 快速修复（推荐）

如果您的开始菜单出现问题，建议直接使用综合修复脚本：

```powershell
# 以管理员身份运行 PowerShell，然后执行：
.\Fix-StartMenu.ps1
```

### 单独使用各个工具

#### 1. 修复图标缓存
```powershell
# 修复图标显示异常问题
.\Fix-IconCache.ps1

# 强制执行（跳过确认）
.\Fix-IconCache.ps1 -Force
```

#### 2. 备份开始菜单
```powershell
# 备份到默认位置
.\Backup-StartMenu.ps1

# 备份到指定位置
.\Backup-StartMenu.ps1 -BackupPath "D:\MyBackups"
```

#### 3. 恢复开始菜单
```powershell
# 交互式恢复
.\Restore-StartMenu.ps1

# 恢复指定日期的备份
.\Restore-StartMenu.ps1 -RestoreDate "20241201"
```

#### 4. 设置自动维护
```powershell
# 设置自动维护任务
.\Setup-StartMenuMaintenance.ps1

# 自定义备份和维护间隔
.\Setup-StartMenuMaintenance.ps1 -BackupInterval 3 -MaintenanceInterval 15

# 移除所有维护任务
.\Setup-StartMenuMaintenance.ps1 -Remove
```

## 🔧 常见问题解决方案

### 问题1: 磁贴图标显示过大
**原因**: Windows 图标缓存损坏
**解决方案**: 
```powershell
.\Fix-IconCache.ps1 -Force
```

### 问题2: 磁贴突然消失
**原因**: 
- 清理注册表时误删相关项
- 开始菜单布局文件损坏
- 应用程序快捷方式路径失效

**解决方案**: 
```powershell
# 如果有备份，先尝试恢复
.\Restore-StartMenu.ps1

# 如果没有备份，使用综合修复
.\Fix-StartMenu.ps1
```

### 问题3: 如何预防磁贴消失
**解决方案**: 设置自动维护任务
```powershell
.\Setup-StartMenuMaintenance.ps1
```

## 📋 系统要求

- **操作系统**: Windows 10/11
- **权限**: 管理员权限（部分功能需要）
- **PowerShell**: 5.0 或更高版本

## ⚠️ 注意事项

1. **管理员权限**: 大部分脚本需要管理员权限才能正常工作
2. **备份重要性**: 在进行任何修复操作前，建议先创建备份
3. **重启建议**: 修复完成后建议重启计算机以确保更改生效
4. **兼容性**: 脚本已在 Windows 10/11 上测试，其他版本可能需要调整

## 🔍 故障排除

### 脚本执行策略问题
如果遇到执行策略限制，请以管理员身份运行：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 权限不足问题
确保以管理员身份运行 PowerShell：
1. 右键点击 PowerShell 图标
2. 选择"以管理员身份运行"
3. 在 UAC 提示中点击"是"

### 日志文件位置
所有脚本都会生成详细的日志文件，位置在：
- `%TEMP%\IconCacheFix.log` - 图标缓存修复日志
- `%TEMP%\StartMenuBackup.log` - 备份操作日志
- `%TEMP%\StartMenuRestore.log` - 恢复操作日志
- `%TEMP%\StartMenuFix.log` - 综合修复日志
- `%TEMP%\StartMenuMaintenance.log` - 维护任务设置日志

## 📞 技术支持

如果您在使用过程中遇到问题：

1. **查看日志文件** - 检查相应的日志文件获取详细错误信息
2. **重启尝试** - 重启计算机后重新运行脚本
3. **安全模式** - 在安全模式下尝试运行修复脚本
4. **系统还原** - 如果问题严重，考虑使用系统还原点

## 📝 更新日志

### v1.0 (2024-12-01)
- 初始版本发布
- 包含完整的修复和维护功能
- 支持自动备份和恢复
- 添加定期维护任务

## 📄 许可证

本工具集仅供学习和个人使用。使用本工具造成的任何损失，作者不承担责任。建议在使用前创建系统还原点。
