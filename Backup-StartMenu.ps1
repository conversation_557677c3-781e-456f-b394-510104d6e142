# Windows 开始菜单备份脚本
# 备份开始菜单布局和磁贴配置
# 作者: AI助手
# 版本: 1.0

param(
    [string]$BackupPath = "$env:USERPROFILE\Documents\StartMenuBackups",
    [switch]$Verbose
)

# 写入日志
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    if ($Verbose) {
        Add-Content -Path "$env:TEMP\StartMenuBackup.log" -Value $logMessage
    }
}

# 创建备份目录
function New-BackupDirectory {
    param([string]$Path)
    
    try {
        if (-not (Test-Path $Path)) {
            New-Item -Path $Path -ItemType Directory -Force | Out-Null
            Write-Log "创建备份目录: $Path"
        }
        return $true
    }
    catch {
        Write-Log "创建备份目录失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 备份开始菜单布局
function Backup-StartMenuLayout {
    param([string]$BackupDir)
    
    Write-Log "开始备份开始菜单布局..."
    
    try {
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $layoutBackupPath = Join-Path $BackupDir "StartMenuLayout_$timestamp.xml"
        
        # 导出开始菜单布局
        Export-StartLayout -Path $layoutBackupPath
        
        if (Test-Path $layoutBackupPath) {
            Write-Log "开始菜单布局已备份到: $layoutBackupPath"
            return $layoutBackupPath
        } else {
            Write-Log "开始菜单布局备份失败" "ERROR"
            return $null
        }
    }
    catch {
        Write-Log "备份开始菜单布局时出错: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# 备份开始菜单快捷方式
function Backup-StartMenuShortcuts {
    param([string]$BackupDir)
    
    Write-Log "开始备份开始菜单快捷方式..."
    
    try {
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $shortcutsBackupPath = Join-Path $BackupDir "StartMenuShortcuts_$timestamp"
        
        # 备份用户开始菜单
        $userStartMenu = "$env:APPDATA\Microsoft\Windows\Start Menu"
        if (Test-Path $userStartMenu) {
            $userBackupPath = Join-Path $shortcutsBackupPath "User"
            Copy-Item -Path $userStartMenu -Destination $userBackupPath -Recurse -Force
            Write-Log "用户开始菜单已备份到: $userBackupPath"
        }
        
        # 备份公共开始菜单
        $commonStartMenu = "$env:ProgramData\Microsoft\Windows\Start Menu"
        if (Test-Path $commonStartMenu) {
            $commonBackupPath = Join-Path $shortcutsBackupPath "Common"
            Copy-Item -Path $commonStartMenu -Destination $commonBackupPath -Recurse -Force
            Write-Log "公共开始菜单已备份到: $commonBackupPath"
        }
        
        return $shortcutsBackupPath
    }
    catch {
        Write-Log "备份开始菜单快捷方式时出错: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# 备份注册表相关项
function Backup-StartMenuRegistry {
    param([string]$BackupDir)
    
    Write-Log "开始备份开始菜单相关注册表..."
    
    try {
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $regBackupPath = Join-Path $BackupDir "StartMenuRegistry_$timestamp.reg"
        
        # 导出开始菜单相关注册表项
        $regKeys = @(
            "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced",
            "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\CloudStore",
            "HKEY_CURRENT_USER\Software\Classes\Local Settings\Software\Microsoft\Windows\CurrentVersion\AppModel"
        )
        
        $regContent = @()
        $regContent += "Windows Registry Editor Version 5.00"
        $regContent += ""
        
        foreach ($key in $regKeys) {
            try {
                $output = reg export $key "$env:TEMP\temp_$($key.Replace('\','_').Replace(':','_')).reg" /y 2>$null
                if (Test-Path "$env:TEMP\temp_$($key.Replace('\','_').Replace(':','_')).reg") {
                    $content = Get-Content "$env:TEMP\temp_$($key.Replace('\','_').Replace(':','_')).reg" -Encoding Unicode
                    $regContent += $content[2..$content.Length]  # 跳过头部
                    $regContent += ""
                    Remove-Item "$env:TEMP\temp_$($key.Replace('\','_').Replace(':','_')).reg" -Force
                }
            }
            catch {
                Write-Log "导出注册表项 $key 失败: $($_.Exception.Message)" "WARNING"
            }
        }
        
        $regContent | Out-File -FilePath $regBackupPath -Encoding Unicode
        Write-Log "开始菜单注册表已备份到: $regBackupPath"
        return $regBackupPath
    }
    catch {
        Write-Log "备份开始菜单注册表时出错: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# 创建备份信息文件
function New-BackupInfo {
    param([string]$BackupDir, [hashtable]$BackupFiles)
    
    try {
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $infoPath = Join-Path $BackupDir "BackupInfo_$timestamp.txt"
        
        $info = @()
        $info += "开始菜单备份信息"
        $info += "备份时间: $(Get-Date)"
        $info += "计算机名: $env:COMPUTERNAME"
        $info += "用户名: $env:USERNAME"
        $info += "Windows版本: $((Get-WmiObject Win32_OperatingSystem).Caption)"
        $info += ""
        $info += "备份文件:"
        
        foreach ($key in $BackupFiles.Keys) {
            if ($BackupFiles[$key]) {
                $info += "  $key : $($BackupFiles[$key])"
            }
        }
        
        $info | Out-File -FilePath $infoPath -Encoding UTF8
        Write-Log "备份信息已保存到: $infoPath"
        return $infoPath
    }
    catch {
        Write-Log "创建备份信息文件失败: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# 清理旧备份
function Remove-OldBackups {
    param([string]$BackupDir, [int]$KeepDays = 30)
    
    Write-Log "清理 $KeepDays 天前的旧备份..."
    
    try {
        $cutoffDate = (Get-Date).AddDays(-$KeepDays)
        $oldFiles = Get-ChildItem -Path $BackupDir -File | Where-Object { $_.CreationTime -lt $cutoffDate }
        $oldDirs = Get-ChildItem -Path $BackupDir -Directory | Where-Object { $_.CreationTime -lt $cutoffDate }
        
        $removedCount = 0
        
        foreach ($file in $oldFiles) {
            Remove-Item -Path $file.FullName -Force
            $removedCount++
        }
        
        foreach ($dir in $oldDirs) {
            Remove-Item -Path $dir.FullName -Recurse -Force
            $removedCount++
        }
        
        if ($removedCount -gt 0) {
            Write-Log "已清理 $removedCount 个旧备份文件/目录"
        } else {
            Write-Log "没有需要清理的旧备份"
        }
    }
    catch {
        Write-Log "清理旧备份时出错: $($_.Exception.Message)" "WARNING"
    }
}

# 主函数
function Main {
    Write-Log "=== Windows 开始菜单备份工具启动 ==="
    
    # 创建备份目录
    if (-not (New-BackupDirectory -Path $BackupPath)) {
        Write-Host "无法创建备份目录，备份终止。" -ForegroundColor Red
        return
    }
    
    $backupFiles = @{}
    
    # 执行各项备份
    Write-Log "开始执行备份操作..."
    
    $backupFiles["布局文件"] = Backup-StartMenuLayout -BackupDir $BackupPath
    $backupFiles["快捷方式"] = Backup-StartMenuShortcuts -BackupDir $BackupPath
    $backupFiles["注册表"] = Backup-StartMenuRegistry -BackupDir $BackupPath
    
    # 创建备份信息文件
    $backupFiles["信息文件"] = New-BackupInfo -BackupDir $BackupPath -BackupFiles $backupFiles
    
    # 清理旧备份
    Remove-OldBackups -BackupDir $BackupPath
    
    # 统计结果
    $successCount = ($backupFiles.Values | Where-Object { $_ -ne $null }).Count
    $totalCount = $backupFiles.Count
    
    Write-Log "=== 备份完成 ==="
    Write-Host "`n备份结果: $successCount/$totalCount 项成功" -ForegroundColor $(if($successCount -eq $totalCount){"Green"}else{"Yellow"})
    Write-Host "备份位置: $BackupPath" -ForegroundColor Cyan
    
    if ($Verbose) {
        Write-Host "详细日志: $env:TEMP\StartMenuBackup.log" -ForegroundColor Gray
    }
}

# 执行主函数
Main
