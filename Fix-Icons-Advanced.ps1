# Advanced Windows Icon Cache Fix
# For Cursor and Windsurf icon display issues
# Must run as Administrator

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires Administrator privileges!" -ForegroundColor Red
    Write-Host "Please run PowerShell as Administrator and try again." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "=== Advanced Icon Cache Fix Tool ===" -ForegroundColor Cyan
Write-Host "Fixing Cursor and Windsurf icon display issues..." -ForegroundColor White
Write-Host ""

try {
    # Step 1: Stop all related processes
    Write-Host "Step 1: Stopping related processes..." -ForegroundColor Yellow
    Get-Process -Name "explorer" -ErrorAction SilentlyContinue | Stop-Process -Force
    Get-Process -Name "dwm" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 3
    Write-Host "✓ Processes stopped" -ForegroundColor Green

    # Step 2: Clear icon cache files with admin privileges
    Write-Host "Step 2: Clearing icon cache files..." -ForegroundColor Yellow
    
    $cachePaths = @(
        "$env:LOCALAPPDATA\IconCache.db",
        "$env:LOCALAPPDATA\Microsoft\Windows\Explorer\*.db",
        "$env:LOCALAPPDATA\Packages\Microsoft.Windows.StartMenuExperienceHost_*\TempState\*",
        "$env:PROGRAMDATA\Microsoft\Windows\AppRepository\*"
    )
    
    foreach ($path in $cachePaths) {
        try {
            Remove-Item -Path $path -Force -Recurse -ErrorAction SilentlyContinue
            Write-Host "  Cleared: $path" -ForegroundColor Gray
        }
        catch {
            Write-Host "  Warning: Could not clear $path" -ForegroundColor DarkYellow
        }
    }
    Write-Host "✓ Cache files cleared" -ForegroundColor Green

    # Step 3: Reset Start Menu database
    Write-Host "Step 3: Resetting Start Menu database..." -ForegroundColor Yellow
    
    # Re-register Start Menu components
    $commands = @(
        'Get-AppXPackage -AllUsers | Foreach {Add-AppxPackage -DisableDevelopmentMode -Register "$($_.InstallLocation)\AppXManifest.xml" -ErrorAction SilentlyContinue}',
        'Get-AppXPackage -Name Microsoft.Windows.StartMenuExperienceHost | Foreach {Add-AppxPackage -DisableDevelopmentMode -Register "$($_.InstallLocation)\AppXManifest.xml" -ErrorAction SilentlyContinue}'
    )
    
    foreach ($cmd in $commands) {
        try {
            Invoke-Expression $cmd
            Write-Host "  Executed registration command" -ForegroundColor Gray
        }
        catch {
            Write-Host "  Warning: Registration command failed" -ForegroundColor DarkYellow
        }
    }
    Write-Host "✓ Start Menu database reset" -ForegroundColor Green

    # Step 4: Clear thumbnail cache
    Write-Host "Step 4: Clearing thumbnail cache..." -ForegroundColor Yellow
    Remove-Item -Path "$env:LOCALAPPDATA\Microsoft\Windows\Explorer\thumbcache_*.db" -Force -ErrorAction SilentlyContinue
    Write-Host "✓ Thumbnail cache cleared" -ForegroundColor Green

    # Step 5: Restart services and explorer
    Write-Host "Step 5: Restarting services..." -ForegroundColor Yellow
    
    # Restart Windows services
    $services = @("Themes", "UxSms", "ShellHWDetection")
    foreach ($service in $services) {
        try {
            Restart-Service -Name $service -Force -ErrorAction SilentlyContinue
            Write-Host "  Restarted service: $service" -ForegroundColor Gray
        }
        catch {
            Write-Host "  Warning: Could not restart $service" -ForegroundColor DarkYellow
        }
    }
    
    # Start Explorer
    Start-Process "explorer.exe"
    Start-Sleep -Seconds 5
    Write-Host "✓ Services and Explorer restarted" -ForegroundColor Green

    # Step 6: Force refresh desktop and Start Menu
    Write-Host "Step 6: Refreshing desktop and Start Menu..." -ForegroundColor Yellow
    
    # Multiple refresh methods
    rundll32.exe user32.dll,UpdatePerUserSystemParameters
    rundll32.exe shell32.dll,SHChangeNotify,0x8000000,0x1000,0,0
    
    # Refresh Start Menu specifically
    $shell = New-Object -ComObject Shell.Application
    $shell.RefreshMenu()
    
    Write-Host "✓ Desktop and Start Menu refreshed" -ForegroundColor Green

    Write-Host ""
    Write-Host "=== Fix Completed Successfully! ===" -ForegroundColor Green
    Write-Host "Please check your Start Menu tiles now." -ForegroundColor White
    Write-Host "If icons are still not correct, please restart your computer." -ForegroundColor Yellow

}
catch {
    Write-Host ""
    Write-Host "=== Fix Failed ===" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please try restarting your computer manually." -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Press Enter to exit"
