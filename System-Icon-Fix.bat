@echo off
echo ========================================
echo System Icon Cache Repair Tool
echo ========================================
echo.
echo This will use Windows built-in tools to fix icon issues
echo.
pause

echo.
echo Step 1: Running System File Checker...
sfc /scannow

echo.
echo Step 2: Running DISM to repair Windows image...
DISM /Online /Cleanup-Image /RestoreHealth

echo.
echo Step 3: Rebuilding icon cache...
taskkill /f /im explorer.exe
timeout /t 3
del /a /q "%localappdata%\IconCache.db"
del /a /q "%localappdata%\Microsoft\Windows\Explorer\iconcache_*.db"
del /a /q "%localappdata%\Microsoft\Windows\Explorer\thumbcache_*.db"
timeout /t 2
start explorer.exe

echo.
echo Step 4: Refreshing system...
rundll32.exe user32.dll,UpdatePerUserSystemParameters

echo.
echo ========================================
echo System repair completed!
echo ========================================
echo.
echo Please restart your computer for best results.
echo.
pause
