Cursor和Windsurf磁贴图标显示过大 - 手动修复步骤
=======================================================

由于自动修复脚本遇到技术问题，请按照以下步骤手动修复：

方法一：直接调整磁贴大小（最简单）
=====================================
1. 打开开始菜单
2. 找到Cursor磁贴，右键点击
3. 选择"调整大小" → "小" 或 "中"
4. 对Windsurf磁贴重复相同操作
5. 如果没有"调整大小"选项，继续下面的方法

方法二：重新固定磁贴
=====================================
1. 打开开始菜单
2. 右键点击Cursor磁贴 → "从'开始'屏幕取消固定"
3. 右键点击Windsurf磁贴 → "从'开始'屏幕取消固定"
4. 在开始菜单搜索框中输入"Cursor"
5. 右键搜索结果 → "固定到'开始'屏幕"
6. 对Windsurf重复相同操作

方法三：重置开始菜单（如果上述方法无效）
=====================================
1. 按 Win + R，输入：powershell
2. 在PowerShell中输入以下命令：
   Get-AppXPackage -AllUsers | Foreach {Add-AppxPackage -DisableDevelopmentMode -Register "$($_.InstallLocation)\AppXManifest.xml"}
3. 等待命令执行完成（可能需要几分钟）
4. 重启计算机

方法四：检查DPI设置
=====================================
1. 右键桌面 → "显示设置"
2. 查看"缩放与布局"部分
3. 如果设置为125%或150%，尝试改为100%
4. 注销并重新登录

方法五：重新安装应用程序（最彻底）
=====================================
1. 卸载Cursor：
   - 打开设置 → 应用 → 找到Cursor → 卸载
2. 卸载Windsurf：
   - 打开设置 → 应用 → 找到Windsurf → 卸载
3. 重启计算机
4. 重新下载并安装最新版本的Cursor和Windsurf

方法六：使用注册表编辑器（高级用户）
=====================================
1. 按 Win + R，输入：regedit
2. 导航到：HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\CloudStore
3. 删除CloudStore文件夹（这会重置开始菜单布局）
4. 重启计算机

故障排除提示
=====================================
- 如果图标仍然显示异常，可能是应用程序本身的图标资源有问题
- 尝试更新Windows系统到最新版本
- 检查是否有Windows更新待安装
- 考虑创建新的用户账户测试问题是否仍然存在

预防措施
=====================================
- 避免使用激进的系统清理工具
- 定期创建系统还原点
- 避免手动删除系统文件和注册表项

如果所有方法都无效
=====================================
这可能表明：
1. 应用程序本身存在兼容性问题
2. Windows系统文件损坏
3. 需要联系应用程序开发者获取支持

建议优先尝试方法一和方法二，这些是最安全且有效的解决方案。
