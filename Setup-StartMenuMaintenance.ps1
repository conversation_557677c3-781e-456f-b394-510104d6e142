# Windows 开始菜单定期维护任务设置脚本
# 创建自动化任务防止开始菜单问题
# 作者: AI助手
# 版本: 1.0

param(
    [switch]$Remove,
    [int]$BackupInterval = 7,  # 备份间隔天数
    [int]$MaintenanceInterval = 30,  # 维护间隔天数
    [switch]$Force
)

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 写入日志
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path "$env:TEMP\StartMenuMaintenance.log" -Value $logMessage
}

# 创建备份任务
function New-BackupTask {
    param([int]$IntervalDays)
    
    try {
        $taskName = "StartMenu-AutoBackup"
        $taskDescription = "自动备份开始菜单配置"
        
        # 检查任务是否已存在
        $existingTask = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue
        if ($existingTask) {
            Write-Log "备份任务已存在，正在更新..."
            Unregister-ScheduledTask -TaskName $taskName -Confirm:$false
        }
        
        # 创建任务动作
        $backupScript = Join-Path $PSScriptRoot "Backup-StartMenu.ps1"
        $action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-WindowStyle Hidden -ExecutionPolicy Bypass -File `"$backupScript`""
        
        # 创建任务触发器 (每N天执行一次)
        $trigger = New-ScheduledTaskTrigger -Daily -DaysInterval $IntervalDays -At "02:00"
        
        # 创建任务设置
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
        
        # 创建任务主体 (以当前用户身份运行)
        $principal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType Interactive
        
        # 注册任务
        Register-ScheduledTask -TaskName $taskName -Description $taskDescription -Action $action -Trigger $trigger -Settings $settings -Principal $principal
        
        Write-Log "备份任务创建成功: $taskName (每 $IntervalDays 天执行)"
        return $true
    }
    catch {
        Write-Log "创建备份任务失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 创建维护任务
function New-MaintenanceTask {
    param([int]$IntervalDays)
    
    try {
        $taskName = "StartMenu-AutoMaintenance"
        $taskDescription = "自动维护开始菜单健康状态"
        
        # 检查任务是否已存在
        $existingTask = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue
        if ($existingTask) {
            Write-Log "维护任务已存在，正在更新..."
            Unregister-ScheduledTask -TaskName $taskName -Confirm:$false
        }
        
        # 创建维护脚本
        $maintenanceScript = Join-Path $PSScriptRoot "StartMenu-Maintenance.ps1"
        New-MaintenanceScript -ScriptPath $maintenanceScript
        
        # 创建任务动作
        $action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-WindowStyle Hidden -ExecutionPolicy Bypass -File `"$maintenanceScript`""
        
        # 创建任务触发器 (每N天执行一次)
        $trigger = New-ScheduledTaskTrigger -Daily -DaysInterval $IntervalDays -At "03:00"
        
        # 创建任务设置
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
        
        # 创建任务主体 (以当前用户身份运行)
        $principal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType Interactive
        
        # 注册任务
        Register-ScheduledTask -TaskName $taskName -Description $taskDescription -Action $action -Trigger $trigger -Settings $settings -Principal $principal
        
        Write-Log "维护任务创建成功: $taskName (每 $IntervalDays 天执行)"
        return $true
    }
    catch {
        Write-Log "创建维护任务失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 创建维护脚本
function New-MaintenanceScript {
    param([string]$ScriptPath)
    
    $maintenanceContent = @'
# 开始菜单自动维护脚本
# 由 Setup-StartMenuMaintenance.ps1 自动生成

$logFile = "$env:TEMP\StartMenuAutoMaintenance.log"

function Write-MaintenanceLog {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] $Message"
    Add-Content -Path $logFile -Value $logMessage
}

try {
    Write-MaintenanceLog "开始菜单自动维护开始"
    
    # 检查开始菜单健康状态
    $startMenuProcess = Get-Process -Name "StartMenuExperienceHost" -ErrorAction SilentlyContinue
    if (-not $startMenuProcess) {
        Write-MaintenanceLog "检测到开始菜单进程异常，尝试修复"
        
        # 重启资源管理器
        Get-Process -Name "explorer" -ErrorAction SilentlyContinue | Stop-Process -Force
        Start-Sleep -Seconds 3
        Start-Process "explorer.exe"
        
        Write-MaintenanceLog "已重启Windows资源管理器"
    }
    
    # 清理临时缓存
    $tempPaths = @(
        "$env:LOCALAPPDATA\Microsoft\Windows\Caches",
        "$env:TEMP\*.tmp"
    )
    
    foreach ($path in $tempPaths) {
        try {
            $items = Get-ChildItem -Path $path -Force -ErrorAction SilentlyContinue
            foreach ($item in $items) {
                if ($item.CreationTime -lt (Get-Date).AddDays(-7)) {
                    Remove-Item -Path $item.FullName -Recurse -Force -ErrorAction SilentlyContinue
                }
            }
        }
        catch {
            # 忽略清理错误
        }
    }
    
    Write-MaintenanceLog "开始菜单自动维护完成"
}
catch {
    Write-MaintenanceLog "开始菜单自动维护失败: $($_.Exception.Message)"
}
'@
    
    try {
        $maintenanceContent | Out-File -FilePath $ScriptPath -Encoding UTF8 -Force
        Write-Log "维护脚本已创建: $ScriptPath"
        return $true
    }
    catch {
        Write-Log "创建维护脚本失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 移除所有任务
function Remove-AllTasks {
    try {
        $taskNames = @("StartMenu-AutoBackup", "StartMenu-AutoMaintenance")
        $removedCount = 0
        
        foreach ($taskName in $taskNames) {
            $task = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue
            if ($task) {
                Unregister-ScheduledTask -TaskName $taskName -Confirm:$false
                Write-Log "已移除任务: $taskName"
                $removedCount++
            }
        }
        
        # 移除维护脚本
        $maintenanceScript = Join-Path $PSScriptRoot "StartMenu-Maintenance.ps1"
        if (Test-Path $maintenanceScript) {
            Remove-Item -Path $maintenanceScript -Force
            Write-Log "已移除维护脚本: $maintenanceScript"
        }
        
        Write-Log "共移除 $removedCount 个任务"
        return $true
    }
    catch {
        Write-Log "移除任务失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 显示当前任务状态
function Show-TaskStatus {
    Write-Host "`n当前开始菜单维护任务状态:" -ForegroundColor Cyan
    
    $taskNames = @("StartMenu-AutoBackup", "StartMenu-AutoMaintenance")
    
    foreach ($taskName in $taskNames) {
        $task = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue
        if ($task) {
            $lastRun = (Get-ScheduledTaskInfo -TaskName $taskName).LastRunTime
            $nextRun = (Get-ScheduledTaskInfo -TaskName $taskName).NextRunTime
            $state = $task.State
            
            Write-Host "  $taskName :" -ForegroundColor White
            Write-Host "    状态: $state" -ForegroundColor $(if($state -eq "Ready"){"Green"}else{"Yellow"})
            Write-Host "    上次运行: $lastRun" -ForegroundColor Gray
            Write-Host "    下次运行: $nextRun" -ForegroundColor Gray
        } else {
            Write-Host "  $taskName : 未安装" -ForegroundColor Red
        }
    }
}

# 主函数
function Main {
    Write-Log "=== Windows 开始菜单维护任务设置工具启动 ==="
    
    if (-not (Test-Administrator)) {
        Write-Log "此脚本需要管理员权限运行" "ERROR"
        Write-Host "请以管理员身份运行PowerShell，然后重新执行此脚本。" -ForegroundColor Red
        return
    }
    
    Write-Host "Windows 开始菜单维护任务设置工具" -ForegroundColor Cyan
    
    if ($Remove) {
        Write-Host "正在移除所有开始菜单维护任务..." -ForegroundColor Yellow
        
        if (-not $Force) {
            $confirmation = Read-Host "确认移除所有维护任务？(Y/N)"
            if ($confirmation -ne 'Y' -and $confirmation -ne 'y') {
                Write-Log "用户取消移除操作"
                return
            }
        }
        
        if (Remove-AllTasks) {
            Write-Host "所有维护任务已成功移除。" -ForegroundColor Green
        } else {
            Write-Host "移除维护任务时出现错误。" -ForegroundColor Red
        }
        return
    }
    
    # 显示当前状态
    Show-TaskStatus
    
    # 询问用户确认
    if (-not $Force) {
        Write-Host "`n将要创建的维护任务:" -ForegroundColor Yellow
        Write-Host "1. 自动备份任务 - 每 $BackupInterval 天备份开始菜单配置" -ForegroundColor White
        Write-Host "2. 自动维护任务 - 每 $MaintenanceInterval 天检查和维护开始菜单" -ForegroundColor White
        
        $confirmation = Read-Host "`n是否继续创建维护任务？(Y/N)"
        if ($confirmation -ne 'Y' -and $confirmation -ne 'y') {
            Write-Log "用户取消创建操作"
            return
        }
    }
    
    # 创建任务
    $results = @{}
    $results["备份任务"] = New-BackupTask -IntervalDays $BackupInterval
    $results["维护任务"] = New-MaintenanceTask -IntervalDays $MaintenanceInterval
    
    # 显示结果
    $successCount = ($results.Values | Where-Object { $_ }).Count
    $totalCount = $results.Count
    
    Write-Log "=== 任务设置完成 ==="
    Write-Host "`n设置结果: $successCount/$totalCount 项成功" -ForegroundColor $(if($successCount -eq $totalCount){"Green"}else{"Yellow"})
    
    foreach ($key in $results.Keys) {
        $status = if ($results[$key]) { "成功" } else { "失败" }
        $color = if ($results[$key]) { "Green" } else { "Red" }
        Write-Host "  $key : $status" -ForegroundColor $color
    }
    
    if ($successCount -eq $totalCount) {
        Write-Host "`n✓ 开始菜单维护任务设置完成！" -ForegroundColor Green
        Write-Host "系统将自动维护开始菜单健康状态。" -ForegroundColor White
        
        # 显示最终状态
        Show-TaskStatus
    } else {
        Write-Host "`n⚠ 部分任务设置失败，请检查错误日志。" -ForegroundColor Yellow
    }
    
    Write-Host "`n使用说明:" -ForegroundColor Cyan
    Write-Host "• 要移除所有维护任务，请运行: .\Setup-StartMenuMaintenance.ps1 -Remove" -ForegroundColor White
    Write-Host "• 要查看任务状态，请在任务计划程序中查看" -ForegroundColor White
    Write-Host "• 维护日志位置: $env:TEMP\StartMenuAutoMaintenance.log" -ForegroundColor White
    
    Write-Host "`n详细日志: $env:TEMP\StartMenuMaintenance.log" -ForegroundColor Gray
}

# 执行主函数
Main
